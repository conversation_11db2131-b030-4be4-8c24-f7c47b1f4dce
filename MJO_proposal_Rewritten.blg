This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_Rewritten.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apalike.bst
Reallocating 'name_of_file' (item size: 1) to 14 items.
Database file #1: ./mjo_ref.bib
Repeated entry---line 411 of file ./mjo_ref.bib
 : @incollection{alexander2010gravity
 :                                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 558 of file ./mjo_ref.bib
 : @article{alexander2008global
 :                             ,
I'm skipping whatever remains of this entry
Repeated entry---line 568 of file ./mjo_ref.bib
 : @article{alexander2010gravity
 :                              ,
I'm skipping whatever remains of this entry
Repeated entry---line 579 of file ./mjo_ref.bib
 : @article{alexander1998interpretations
 :                                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 590 of file ./mjo_ref.bib
 : @article{alexander2018mjo
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 612 of file ./mjo_ref.bib
 : @article{ayorinde2023
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 623 of file ./mjo_ref.bib
 : @article{ayorinde2024
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 646 of file ./mjo_ref.bib
 : @article{Faber2013
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 657 of file ./mjo_ref.bib
 : @article{fritts2003review
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 678 of file ./mjo_ref.bib
 : @article{ho2022using
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 688 of file ./mjo_ref.bib
 : @article{hood2020stratospheric
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 698 of file ./mjo_ref.bib
 : @article{hood2023qbo
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 708 of file ./mjo_ref.bib
 : @article{li2020saber
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 730 of file ./mjo_ref.bib
 : @article{madden1994observations
 :                                ,
I'm skipping whatever remains of this entry
Repeated entry---line 784 of file ./mjo_ref.bib
 : @article{trencham2024causes
 :                            ,
I'm skipping whatever remains of this entry
Repeated entry---line 794 of file ./mjo_ref.bib
 : @article{Tsuda2000
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 805 of file ./mjo_ref.bib
 : @article{tsuchiya2016mjo
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 816 of file ./mjo_ref.bib
 : @article{wilson1991
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 827 of file ./mjo_ref.bib
 : @article{zhang2005madden
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 837 of file ./mjo_ref.bib
 : @article{zhou2024observed
 :                          ,
I'm skipping whatever remains of this entry
Repeated entry---line 946 of file ./mjo_ref.bib
 : @article{hood2020stratospheric
 :                               ,
I'm skipping whatever remains of this entry
Repeated entry---line 957 of file ./mjo_ref.bib
 : @article{hood2023qbo
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 968 of file ./mjo_ref.bib
 : @article{trencham2024causes
 :                            ,
I'm skipping whatever remains of this entry
Warning--empty journal in Stockwell1996a
You've used 36 entries,
            1935 wiz_defined-function locations,
            873 strings with 13825 characters,
and the built_in function-call counts, 17681 in all, are:
= -- 1660
> -- 979
< -- 10
+ -- 360
- -- 350
* -- 1749
:= -- 3091
add.period$ -- 108
call.type$ -- 36
change.case$ -- 354
chr.to.int$ -- 34
cite$ -- 37
duplicate$ -- 502
empty$ -- 1112
format.name$ -- 398
if$ -- 3334
int.to.chr$ -- 3
int.to.str$ -- 0
missing$ -- 36
newline$ -- 183
num.names$ -- 108
pop$ -- 274
preamble$ -- 1
purify$ -- 354
quote$ -- 0
skip$ -- 372
stack$ -- 0
substring$ -- 1388
swap$ -- 35
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 216
warning$ -- 1
while$ -- 127
width$ -- 0
write$ -- 469
(There were 23 error messages)
