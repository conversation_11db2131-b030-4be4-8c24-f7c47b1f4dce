This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  23 JUN 2025 16:12
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./MJO_proposal_Rewritten.tex
(MJO_proposal_Rewritten.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks19
\inpenc@posthook=\toks20
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 11
6.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen150
\Gin@req@width=\dimen151
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen152
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen153
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen154
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks22
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks23
\eqnshift@=\dimen155
\alignsep@=\dimen156
\tagshift@=\dimen157
\tagwidth@=\dimen158
\totwidth@=\dimen159
\lineht@=\dimen160
\@envbody=\toks24
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count283
\l__siunitx_number_exponent_fixed_int=\count284
\l__siunitx_number_min_decimal_int=\count285
\l__siunitx_number_min_integer_int=\count286
\l__siunitx_number_round_precision_int=\count287
\l__siunitx_number_lower_threshold_int=\count288
\l__siunitx_number_upper_threshold_int=\count289
\l__siunitx_number_group_first_int=\count290
\l__siunitx_number_group_size_int=\count291
\l__siunitx_number_group_minimum_int=\count292
\l__siunitx_angle_tmp_dim=\dimen161
\l__siunitx_angle_marker_box=\box54
\l__siunitx_angle_unit_box=\box55
\l__siunitx_compound_count_int=\count293

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count294
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
\l__siunitx_table_tmp_box=\box56
\l__siunitx_table_tmp_dim=\dimen162
\l__siunitx_table_column_width_dim=\dimen163
\l__siunitx_table_integer_box=\box57
\l__siunitx_table_decimal_box=\box58
\l__siunitx_table_uncert_box=\box59
\l__siunitx_table_before_box=\box60
\l__siunitx_table_after_box=\box61
\l__siunitx_table_before_dim=\dimen164
\l__siunitx_table_carry_dim=\dimen165
\l__siunitx_unit_tmp_int=\count295
\l__siunitx_unit_position_int=\count296
\l__siunitx_unit_total_int=\count297

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen166
\ar@mcellbox=\box62
\extrarowheight=\dimen167
\NC@list=\toks26
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box63
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip56
\bibsep=\skip57
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count298
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen168
\Hy@linkcounter=\count300
\Hy@pagecounter=\count301

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count302

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count303
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen169

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count304
\Field@Width=\dimen170
\Fld@charsize=\dimen171
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count305
\c@Item=\count306
\c@Hfootnote=\count307
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count308
\c@bookmark@seq@number=\count309

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip58
)
Package hyperref Info: Option `colorlinks' set `true' on input line 21.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip59
\enit@outerparindent=\dimen172
\enit@toks=\toks27
\enit@inbox=\box64
\enit@count@id=\count310
\enitdp@description=\count311
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.st
y
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen173
\captionmargin=\dimen174
\caption@leftmargin=\dimen175
\caption@rightmargin=\dimen176
\caption@width=\dimen177
\caption@indent=\dimen178
\caption@parindent=\dimen179
\caption@hangindent=\dimen180
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count312
\c@continuedfloat=\count313
Package caption Info: hyperref package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.s
ty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen181
\lightrulewidth=\dimen182
\cmidrulewidth=\dimen183
\belowrulesep=\dimen184
\belowbottomsep=\dimen185
\aboverulesep=\dimen186
\abovetopsep=\dimen187
\cmidrulesep=\dimen188
\cmidrulekern=\dimen189
\defaultaddspace=\dimen190
\@cmidla=\count314
\@cmidlb=\count315
\@aboverulesep=\dimen191
\@belowrulesep=\dimen192
\@thisruleclass=\count316
\@lastruleclass=\count317
\@thisrulewidth=\dimen193
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen194
\TX@old@table=\dimen195
\TX@old@col=\dimen196
\TX@target=\dimen197
\TX@delta=\dimen198
\TX@cols=\count318
\TX@ftn=\toks28
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count319
\float@exts=\toks29
\float@box=\box65
\@float@everytoks=\toks30
\@floatcapt=\box66
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/placeins\placeins.s
ty
Package: placeins 2005/04/18  v 2.2
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/sectsty\sectsty.sty
Package: sectsty 2002/02/25 v2.0.2 Commands to change all sectional heading sty
les

LaTeX Warning: Command \underbar  has changed.
               Check if current package is valid.


LaTeX Warning: Command \underline  has changed.
               Check if current package is valid.

) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\babel.sty
Package: babel 2024/06/26 v24.7 The Babel package
\babel@savecnt=\count320
\U@D=\dimen199
\l@unhyphenated=\language79

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\txtbabel.de
f)
\bbl@readstream=\read2
\bbl@dirlevel=\count321

*************************************
* Local config file bblopts.cfg used
*
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/babel-english\engli
sh.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language73). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language73). Reported on input line 108.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel/locale/en\b
abel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.sty
Package: microtype 2024/12/12 v3.2 Micro-typographical refinements (RS)
\MT@toks=\toks31
\MT@tempbox=\box67
\MT@count=\count322
LaTeX Info: Redefining \noprotrusionifhmode on input line 1075.
LaTeX Info: Redefining \leftprotrusion on input line 1076.
\MT@prot@toks=\toks32
LaTeX Info: Redefining \rightprotrusion on input line 1095.
LaTeX Info: Redefining \textls on input line 1437.
\MT@outer@kern=\dimen256
LaTeX Info: Redefining \microtypecontext on input line 2041.
LaTeX Info: Redefining \textmicrotypecontext on input line 2058.
\MT@listname@count=\count323

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
-pdftex.def
File: microtype-pdftex.def 2024/12/12 v3.2 Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip60
)
Package microtype Info: Loading configuration file microtype.cfg.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\microtype
.cfg
File: microtype.cfg 2024/12/12 v3.2 microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3053.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.s
ty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box68
\beforetitleunit=\skip61
\aftertitleunit=\skip62
\ttl@plus=\dimen257
\ttl@minus=\dimen258
\ttl@toksa=\toks33
\titlewidth=\dimen259
\titlewidthlast=\dimen260
\titlewidthfirst=\dimen261
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/diagbox\diagbox.sty
Package: diagbox 2020/02/09 v2.3 Making table heads with diagonal lines
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.sty
Package: pict2e 2020/09/30 v0.4b Improved picture commands (HjG,RN,JT)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.cfg
File: pict2e.cfg 2016/02/05 v0.1u pict2e configuration for teTeX/TeXLive
)
Package pict2e Info: Driver file: pdftex.def on input line 112.
Package pict2e Info: Driver file for pict2e: p2e-pdftex.def on input line 114.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\p2e-pdftex.d
ef
File: p2e-pdftex.def 2016/02/05 v0.1u Driver-dependant file (RN,HjG,JT)
)
\pIIe@GRAPH=\toks34
\@arclen=\dimen262
\@arcrad=\dimen263
\pIIe@tempdima=\dimen264
\pIIe@tempdimb=\dimen265
\pIIe@tempdimc=\dimen266
\pIIe@tempdimd=\dimen267
\pIIe@tempdime=\dimen268
\pIIe@tempdimf=\dimen269
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count324
\calc@Bcount=\count325
\calc@Adimen=\dimen270
\calc@Bdimen=\dimen271
\calc@Askip=\skip63
\calc@Bskip=\skip64
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count326
\calc@Cskip=\skip65
)
\diagbox@boxa=\box69
\diagbox@boxb=\box70
\diagbox@boxm=\box71
\diagbox@wd=\dimen272
\diagbox@ht=\dimen273
\diagbox@insepl=\dimen274
\diagbox@insepr=\dimen275
\diagbox@outsepl=\dimen276
\diagbox@outsepr=\dimen277
)

Package siunitx Info: Option "detect-weight" has been deprecated in this
(siunitx)             release.
(siunitx)             
(siunitx)             Use "reset-text-series = false, text-series-to-math =
(siunitx)             true" as a replacement.


Package siunitx Info: Option "detect-family" has been deprecated in this
(siunitx)             release.
(siunitx)             
(siunitx)             Use "reset-text-family = false, text-family-to-math =
(siunitx)             true" as a replacement.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.s
ty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip66
\f@nch@offset@elh=\skip67
\f@nch@offset@erh=\skip68
\f@nch@offset@olh=\skip69
\f@nch@offset@orh=\skip70
\f@nch@offset@elf=\skip71
\f@nch@offset@erf=\skip72
\f@nch@offset@olf=\skip73
\f@nch@offset@orf=\skip74
\f@nch@height=\skip75
\f@nch@footalignment=\skip76
\f@nch@widthL=\skip77
\f@nch@widthC=\skip78
\f@nch@widthR=\skip79
\@temptokenb=\toks35
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count327
\l__pdf_internal_box=\box72
) (MJO_proposal_Rewritten.aux)
\openout1 = `MJO_proposal_Rewritten.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(42.67912pt, 512.14963pt, 42.67912pt)
* v-part:(T,H,B)=(42.67912pt, 759.6886pt, 42.67912pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=512.14963pt
* \textheight=759.6886pt
* \oddsidemargin=-29.59087pt
* \evensidemargin=-29.59087pt
* \topmargin=-66.59087pt
* \headheight=13.6pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count328
\scratchdimen=\dimen278
\scratchbox=\box73
\nofMPsegments=\count329
\nofMParguments=\count330
\everyMPshowfont=\toks36
\MPscratchCnt=\count331
\MPscratchDim=\dimen279
\MPnumerator=\count332
\makeMPintoPDFobject=\count333
\everyMPtoPDFconversion=\toks37
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 81.
Package hyperref Info: Link coloring ON on input line 81.
 (MJO_proposal_Rewritten.out)
(MJO_proposal_Rewritten.out)
\@outlinefile=\write3
\openout3 = `MJO_proposal_Rewritten.out'.

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: End \AtBeginDocument code.
LaTeX Info: Redefining \microtypecontext on input line 81.
Package microtype Info: Applying patch `item' on input line 81.
Package microtype Info: Applying patch `toc' on input line 81.
Package microtype Info: Applying patch `eqnum' on input line 81.
Package microtype Info: Applying patch `footnote' on input line 81.
Package microtype Info: Applying patch `verbatim' on input line 81.
LaTeX Info: Redefining \microtypesetup on input line 81.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 81.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-ptm.cf
g
File: mt-ptm.cfg 2006/04/20 v1.7 microtype config. file: Times (RS)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-cmr.cf
g
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 111
.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-msa.cf
g
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 111
.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/microtype\mt-msb.cf
g
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)

[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Users/
ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/base/8r.enc}]

[2

]

[3]
<./FaberPicture1.jpg, id=234, 314.97675pt x 201.1515pt>
File: ./FaberPicture1.jpg Graphic file (type jpg)
<use ./FaberPicture1.jpg>
Package pdftex.def Info: ./FaberPicture1.jpg  used on input line 182.
(pdftex.def)             Requested size: 460.93153pt x 294.36626pt.


[4]

[5 <./FaberPicture1.jpg>]
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 2
04.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)

[6]

[7]

[8]
Underfull \hbox (badness 10000) in paragraph at lines 345--345
[]|\T1/ptm/m/n/10 (+20) Delay in ac-cess-
 []


Underfull \hbox (badness 10000) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) ing/processing full
 []


Underfull \hbox (badness 2111) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) 2006-2024 RO dataset
 []


Underfull \hbox (badness 1924) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) (lat-est years, com-pu-ta-
 []


Underfull \hbox (badness 2035) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) 2022) im-me-di-ately. Se-cure ad-e-
 []


Underfull \hbox (badness 10000) in paragraph at lines 345--345
[]|\T1/ptm/m/n/10 (+20) Difficulties in ro-bust
 []


Underfull \hbox (badness 1062) in paragraph at lines 345--345
[]|\T1/ptm/m/n/10 (+20) Focus on cli-ma-to-log-i-cal es-ti-mates.
 []


Underfull \hbox (badness 8019) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) De-velop mul-ti-ple es-ti-ma-tion ap-
 []


Underfull \hbox (badness 1960) in paragraph at lines 345--345
[]|\T1/ptm/m/n/10 (+20) Weak sta-tis-ti-cal sig-nif-
 []


Underfull \hbox (badness 4108) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) i-cance in in-ter-ac-tively
 []


Underfull \hbox (badness 10000) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) strat-i-fied com-pos-ites
 []


Underfull \hbox (badness 10000) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) (MJO-QBO-ENSO-
 []


Underfull \hbox (badness 1990) in paragraph at lines 345--345
\T1/ptm/m/n/10 (+20) Solar) due to re-duced
 []


Underfull \hbox (badness 10000) in paragraph at lines 345--345
[]|\T1/ptm/m/n/10 (+20) Delays in pub-li-ca-
 []



[9]

[10]

[11] (MJO_proposal_Rewritten.bbl

[12

])

[13] (MJO_proposal_Rewritten.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
Package rerunfilecheck Info: File `MJO_proposal_Rewritten.out' has not changed.

(rerunfilecheck)             Checksum: 34328D5478E6AFD42C153D094C576CAD;7378.
 ) 
Here is how much of TeX's memory you used:
 21274 strings out of 473904
 401248 string characters out of 5724713
 1945908 words of memory out of 5000000
 43378 multiletter control sequences out of 15000+600000
 616107 words of font info for 354 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,12n,80p,3996b,1129s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts
/cm/cmmi10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/pu
blic/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fo
nts/type1/public/amsfonts/cm/cmr10.pfb><C:/Users/<USER>/AppData/Local/Progra
ms/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pfb><C:/Users/<USER>/AppData/L
ocal/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Users/<USER>
nde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy8.pfb><C:/
Users/ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/urw/times/utmb8a.pfb>
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/urw/times/utmbi8a
.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/urw/times/ut
mr8a.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/urw/time
s/utmri8a.pfb>
Output written on MJO_proposal_Rewritten.pdf (13 pages, 408988 bytes).
PDF statistics:
 397 PDF objects out of 1000 (max. 8388607)
 81 named destinations out of 1000 (max. 500000)
 50278 words of extra memory for PDF output out of 51595 (max. 10000000)

